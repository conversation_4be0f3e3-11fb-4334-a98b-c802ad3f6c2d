{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/records/records.vue?9bcd", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/records/records.vue?6a8e", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/records/records.vue?9377", "uni-app:///pages/records/records.vue", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/records/records.vue?be29", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/records/records.vue?087f", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/records/records.vue?403a", "webpack:////Users/<USER>/projects/prize-draw-order/prize-draw-order-uniapp/pages/records/records.vue?6229"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userOpenid", "merchantCode", "isLoading", "isRefreshing", "currentTab", "allRecords", "winningRecords", "unclaimedRecords", "unclaimedCount", "imageErrors", "computed", "displayRecords", "primaryColor", "backgroundGradient", "onLoad", "onShow", "onPullDownRefresh", "onReachBottom", "methods", "initPage", "console", "getUserOpenid", "loadAllData", "Promise", "loadAllRecords", "lotteryApi", "res", "loadWinningRecords", "loadUnclaimedRecords", "refreshData", "uni", "title", "icon", "switchTab", "handleRecordClick", "goToClaim", "url", "goToLottery", "showRecordDetail", "content", "showCancel", "confirmText", "getPrizeIcon", "handleIconError", "getStatusText", "getEmptyTitle", "getEmptySubtitle", "formatDateTime", "adjustColor"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACc;AACwB;;;AAG5F;AACgN;AAChN,gBAAgB,iNAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvCA;AAAA;AAAA;AAAA;AAAq0B,CAAgB,qyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC4Fz1B;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACA;MACAC;MACAC;MAEA;MACAC;MACAC;MAEA;MACAC;MAAA;;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MAEA;MACAC;IACA;EACA;EAEAC;IACA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IACA;EACA;EAEAC;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;EAAA,CACA;EAEAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;MACA;MACA;MACA;MAEA;QACA;MACA;;MAEA;MACA;MACA;QACA;MACA;;MAEA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC,aACA,yBACA,6BACA,8BACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAJ;gBAAA;gBAAA,OACAK;cAAA;gBAAAC;gBACAN;gBACA;kBACA;kBACAA;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAP;gBAAA;gBAAA,OACAK;cAAA;gBAAAC;gBACAN;gBACA;kBACA;kBACAA;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAQ;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAR;gBAAA;gBAAA,OACAK;cAAA;gBAAAC;gBACAN;gBACA;kBACA;kBACA;kBACAA;gBACA;kBACAA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAS;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBACAC;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;gBAAA;gBAEA;gBACAF;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAG;MACA;IACA;IAEA;IACAC;MACA;MACA;;MAEA;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACAL;QACAM;MACA;IACA;IAEA;IACAC;MACA;MACA;QACAD;QACA;QACA;QACA;QACA;QACA;UACAA;QACA;MACA;MAEAN;QAAAM;MAAA;IACA;IAEA;IACAE;MACA;MACA;MAEA;MACA;QACAC;MACA;MACAA;MAEA;QACAA;MACA;MAEAT;QACAC;QACAQ;QACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;;MAEA;MACA;QACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;QACA;MACA;IACA;IAIA;IACAC;MACA;QACA;MACA;QACA;QACA;UACA;QACA;QACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;UACA;QACA;UACA;QACA;UACA;MAAA;IAEA;IAEA;IACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvbA;AAAA;AAAA;AAAA;AAAoiD,CAAgB,o6CAAG,EAAC,C;;;;;;;;;;;ACAxjD;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA4jD,CAAgB,47CAAG,EAAC,C;;;;;;;;;;;ACAhlD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/records/records.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/records/records.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./records.vue?vue&type=template&id=00c304e8&scoped=true&\"\nvar renderjs\nimport script from \"./records.vue?vue&type=script&lang=js&\"\nexport * from \"./records.vue?vue&type=script&lang=js&\"\nimport style0 from \"./records.vue?vue&type=style&index=0&lang=scss&\"\nimport style1 from \"./records.vue?vue&type=style&index=1&id=00c304e8&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"00c304e8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/records/records.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./records.vue?vue&type=template&id=00c304e8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.isLoading ? _vm.displayRecords.length : null\n  var m0 = !_vm.isLoading && g0 === 0 ? _vm.getEmptyTitle() : null\n  var m1 = !_vm.isLoading && g0 === 0 ? _vm.getEmptySubtitle() : null\n  var l0 =\n    !_vm.isLoading && !(g0 === 0)\n      ? _vm.__map(_vm.displayRecords, function (record, __i0__) {\n          var $orig = _vm.__get_orig(record)\n          var m2 = _vm.getPrizeIcon(record)\n          var m3 = _vm.formatDateTime(record.drawTime)\n          var m4 = _vm.getStatusText(record)\n          return {\n            $orig: $orig,\n            m2: m2,\n            m3: m3,\n            m4: m4,\n          }\n        })\n      : null\n  var g1 = _vm.displayRecords.length > 0 && !_vm.isLoading\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        m0: m0,\n        m1: m1,\n        l0: l0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./records.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./records.vue?vue&type=script&lang=js&\"", "<template>\n    <view class=\"records-page\">\n\n        <!-- 筛选标签 -->\n        <view class=\"filter-tabs\">\n            <view class=\"tab-item\" :class=\"{ active: currentTab === 'all' }\" @click=\"switchTab('all')\">\n                <text>全部记录</text>\n            </view>\n            <view class=\"tab-item\" :class=\"{ active: currentTab === 'winning' }\" @click=\"switchTab('winning')\">\n                <text>中奖记录</text>\n            </view>\n            <view class=\"tab-item\" :class=\"{ active: currentTab === 'unclaimed' }\" @click=\"switchTab('unclaimed')\">\n                <text>待领取</text>\n                <view class=\"badge\" v-if=\"unclaimedCount > 0\">{{ unclaimedCount }}</view>\n            </view>\n        </view>\n\n        <!-- 加载状态 -->\n        <view class=\"loading-container\" v-if=\"isLoading\">\n            <view class=\"loading-spinner\"></view>\n            <text class=\"loading-text\">加载中...</text>\n        </view>\n\n        <!-- 记录列表 -->\n        <view class=\"records-container\" v-else>\n            <!-- 空状态 -->\n            <view class=\"empty-state\" v-if=\"displayRecords.length === 0\">\n                <image src=\"/static/logo.png\" class=\"empty-image\" mode=\"aspectFit\"></image>\n                <text class=\"empty-title\">{{ getEmptyTitle() }}</text>\n                <text class=\"empty-subtitle\">{{ getEmptySubtitle() }}</text>\n                <view class=\"empty-action\" v-if=\"currentTab !== 'all'\">\n                    <view class=\"btn btn-primary\" @click=\"goToLottery\">\n                        <text>去抽奖</text>\n                    </view>\n                </view>\n            </view>\n\n            <!-- 记录列表 -->\n            <view class=\"records-list\" v-else>\n                <view class=\"record-item\" v-for=\"record in displayRecords\" :key=\"record.recordId\"\n                    @click=\"handleRecordClick(record)\">\n                    <!-- 记录卡片 -->\n                    <view class=\"record-card\">\n                        <!-- 奖品信息 -->\n                        <view class=\"prize-info\">\n                            <view class=\"prize-icon\">\n                                <image :src=\"getPrizeIcon(record)\" class=\"icon-img\" mode=\"aspectFit\"\n                                    @error=\"handleIconError\"></image>\n                            </view>\n                            <view class=\"prize-details\">\n                                <text class=\"prize-name\">{{ record.prizeName }}</text>\n                                <text class=\"prize-desc\" v-if=\"record.prizeDesc\">{{ record.prizeDesc }}</text>\n                                <text class=\"draw-time\">{{ formatDateTime(record.drawTime) }}</text>\n                            </view>\n                        </view>\n\n                        <!-- 状态标签 -->\n                        <view class=\"status-section\">\n                            <view class=\"status-badge\" :class=\"{\n                                'claimed': record.isWinner === '1' && record.claimStatus === '1',\n                                'unclaimed': record.isWinner === '1' && record.claimStatus !== '1' && record.prizeType !== 'thanks' && record.prizeName !== '谢谢惠顾' && record.prizeName !== '谢谢参与',\n                                'thanks': (record.prizeType === 'thanks' || record.prizeName === '谢谢惠顾' || record.prizeName === '谢谢参与') && record.isWinner !== '1',\n                                'not-won': record.isWinner !== '1' && record.prizeType !== 'thanks' && record.prizeName !== '谢谢惠顾' && record.prizeName !== '谢谢参与'\n                            }\">\n                                <text class=\"status-text\">{{ getStatusText(record) }}</text>\n                            </view>\n\n\n                        </view>\n                    </view>\n\n                    <!-- 活动信息 -->\n                    <view class=\"activity-info\" v-if=\"record.activityName\">\n                        <text class=\"activity-name\">活动：{{ record.activityName }}</text>\n                    </view>\n                </view>\n            </view>\n        </view>\n\n        <!-- 下拉刷新提示 -->\n        <view class=\"refresh-tip\" v-if=\"isRefreshing\">\n            <text>正在刷新...</text>\n        </view>\n\n        <!-- 底部提示 -->\n        <view class=\"bottom-tip\" v-if=\"displayRecords.length > 0 && !isLoading\">\n            <text>已显示全部记录</text>\n        </view>\n    </view>\n</template>\n\n<script>\nimport { lotteryApi, getImageUrl } from '@/utils/api.js'\nimport { formatDateTime, showLoading, hideLoading, showError, getUserInfo } from '@/utils/utils.js'\n\nexport default {\n    data() {\n        return {\n            // 用户信息\n            userOpenid: '',\n            merchantCode: '',\n\n            // 页面状态\n            isLoading: true,\n            isRefreshing: false,\n\n            // 标签页\n            currentTab: 'all', // all, winning, unclaimed\n\n            // 记录数据\n            allRecords: [],\n            winningRecords: [],\n            unclaimedRecords: [],\n\n            // 统计数据\n            unclaimedCount: 0,\n\n            // 错误处理\n            imageErrors: new Set()\n        }\n    },\n\n    computed: {\n        // 当前显示的记录\n        displayRecords() {\n            switch (this.currentTab) {\n                case 'winning':\n                    return this.winningRecords\n                case 'unclaimed':\n                    return this.unclaimedRecords\n                default:\n                    return this.allRecords\n            }\n        },\n\n        // 主题色彩\n        primaryColor() {\n            return '#667eea' // 默认主题色，可以从配置中获取\n        },\n\n        // 背景渐变色\n        backgroundGradient() {\n            const color = this.primaryColor\n            // 生成基于主题色的渐变背景\n            return `linear-gradient(135deg, ${color} 0%, ${this.adjustColor(color, -20)} 100%)`\n        }\n    },\n\n    onLoad(options) {\n        this.merchantCode = options.merchantCode || 'system'\n        if (options.userOpenid) {\n            this.userOpenid = options.userOpenid\n        }\n        this.initPage()\n    },\n    onShow() {\n        this.initPage()\n    },\n\n    onPullDownRefresh() {\n        this.refreshData()\n    },\n\n    onReachBottom() {\n        // 可以在这里实现分页加载\n    },\n\n    methods: {\n        async initPage() {\n            try {\n                // 获取用户openid（实际项目中需要通过微信登录获取）\n                this.userOpenid = this.getUserOpenid()\n\n                if (!this.userOpenid) {\n                    showError('用户信息获取失败')\n                    return\n                }\n\n                await this.loadAllData()\n            } catch (error) {\n                console.error('页面初始化失败:', error)\n                showError('页面加载失败')\n            } finally {\n                this.isLoading = false\n            }\n        },\n\n        // 获取用户openid\n        getUserOpenid() {\n            // 优先从URL参数获取（测试用）\n            const pages = getCurrentPages()\n            const currentPage = pages[pages.length - 1]\n            const options = currentPage.options || {}\n\n            if (options.userOpenid) {\n                return options.userOpenid\n            }\n\n            // 从本地存储获取\n            const userInfo = getUserInfo()\n            if (userInfo.openid) {\n                return userInfo.openid\n            }\n\n            // 测试用固定openid\n            return 'test_user_001'\n        },\n\n        // 加载所有数据\n        async loadAllData() {\n            await Promise.all([\n                this.loadAllRecords(),\n                this.loadWinningRecords(),\n                this.loadUnclaimedRecords()\n            ])\n        },\n\n        // 加载全部记录\n        async loadAllRecords() {\n            try {\n                console.log('开始加载全部记录，商家编码:', this.merchantCode, '用户openid:', this.userOpenid)\n                const res = await lotteryApi.getUserRecordsByMerchant(this.merchantCode, this.userOpenid)\n                console.log('获取全部记录响应:', res)\n                if (res.code === 200) {\n                    this.allRecords = res.data || []\n                    console.log('全部记录数据:', this.allRecords)\n                } else {\n                    console.error('获取全部记录失败，错误码:', res.code, '错误信息:', res.msg)\n                }\n            } catch (error) {\n                console.error('获取全部记录失败:', error)\n                console.error('错误详情:', JSON.stringify(error))\n            }\n        },\n\n        // 加载中奖记录\n        async loadWinningRecords() {\n            try {\n                console.log('开始加载中奖记录，商家编码:', this.merchantCode, '用户openid:', this.userOpenid)\n                const res = await lotteryApi.getUserWinningRecordsByMerchant(this.merchantCode, this.userOpenid)\n                console.log('获取中奖记录响应:', res)\n                if (res.code === 200) {\n                    this.winningRecords = res.data || []\n                    console.log('中奖记录数据:', this.winningRecords)\n                } else {\n                    console.error('获取中奖记录失败，错误码:', res.code, '错误信息:', res.msg)\n                }\n            } catch (error) {\n                console.error('获取中奖记录失败:', error)\n                console.error('错误详情:', JSON.stringify(error))\n            }\n        },\n\n        // 加载未领取记录\n        async loadUnclaimedRecords() {\n            try {\n                console.log('开始加载未领取记录，商家编码:', this.merchantCode, '用户openid:', this.userOpenid)\n                const res = await lotteryApi.getUserUnclaimedRecordsByMerchant(this.merchantCode, this.userOpenid)\n                console.log('获取未领取记录响应:', res)\n                if (res.code === 200) {\n                    this.unclaimedRecords = res.data || []\n                    this.unclaimedCount = this.unclaimedRecords.length\n                    console.log('未领取记录数据:', this.unclaimedRecords)\n                } else {\n                    console.error('获取未领取记录失败，错误码:', res.code, '错误信息:', res.msg)\n                }\n            } catch (error) {\n                console.error('获取未领取记录失败:', error)\n                console.error('错误详情:', JSON.stringify(error))\n            }\n        },\n\n        // 刷新数据\n        async refreshData() {\n            this.isRefreshing = true\n            try {\n                await this.loadAllData()\n                uni.showToast({\n                    title: '刷新成功',\n                    icon: 'success'\n                })\n            } catch (error) {\n                showError('刷新失败')\n            } finally {\n                this.isRefreshing = false\n                uni.stopPullDownRefresh()\n            }\n        },\n\n        // 切换标签页\n        switchTab(tab) {\n            this.currentTab = tab\n        },\n\n        // 处理记录点击\n        handleRecordClick(record) {\n            // 检查是否是谢谢参与类型\n            const isThanks = record.prizeType === 'thanks' || record.prizeName === '谢谢惠顾' || record.prizeName === '谢谢参与'\n\n            // 如果是中奖且未领取，且不是谢谢参与类型，跳转到领取页面\n            if (record.isWinner === '1' && record.claimStatus === '0' && !isThanks) {\n                this.goToClaim(record)\n            } else {\n                // 显示记录详情\n                this.showRecordDetail(record)\n            }\n        },\n\n        // 跳转到领取页面\n        goToClaim(record) {\n            uni.navigateTo({\n                url: `/pages/claim/claim?recordId=${record.recordId}`\n            })\n        },\n\n        // 跳转到抽奖页面\n        goToLottery() {\n            let url = '/pages/lottery/lottery'\n            if (this.merchantCode) {\n                url += `?merchantCode=${this.merchantCode}`\n                // 如果有桌台号参数也传递\n                const pages = getCurrentPages()\n                const currentPage = pages[pages.length - 1]\n                const options = currentPage.options || {}\n                if (options.tableNumber) {\n                    url += `&tableNumber=${options.tableNumber}`\n                }\n            }\n\n            uni.navigateTo({ url })\n        },\n\n        // 显示记录详情\n        showRecordDetail(record) {\n            const statusText = this.getStatusText(record)\n            const timeText = formatDateTime(record.drawTime)\n\n            let content = `奖品：${record.prizeName}\\n`\n            if (record.prizeDesc) {\n                content += `描述：${record.prizeDesc}\\n`\n            }\n            content += `时间：${timeText}\\n状态：${statusText}`\n\n            if (record.activityName) {\n                content += `\\n活动：${record.activityName}`\n            }\n\n            uni.showModal({\n                title: '记录详情',\n                content: content,\n                showCancel: false,\n                confirmText: '确定'\n            })\n        },\n\n        // 获取奖品图标\n        getPrizeIcon(record) {\n            // 如果有自定义图标且没有加载错误，使用自定义图标\n            if (record.prizeImage && !this.imageErrors.has(record.prizeImage)) {\n                return getImageUrl(record.prizeImage)\n            }\n\n            // 根据中奖状态返回默认图标\n            if (record.isWinner === '1') {\n                return '/static/logo.png' // 使用现有的logo作为默认图标\n            } else {\n                return '/static/logo.png' // 使用现有的logo作为默认图标\n            }\n        },\n\n        // 处理图标加载错误\n        handleIconError(e) {\n            const src = e.target.src || e.detail.src\n            if (src) {\n                this.imageErrors.add(src)\n            }\n        },\n\n\n\n        // 获取状态文本\n        getStatusText(record) {\n            if (record.isWinner === '1') {\n                return record.claimStatus === '1' ? '已领取' : '待领取'\n            } else {\n                // 检查是否是谢谢参与类型\n                if (record.prizeType === 'thanks' || record.prizeName === '谢谢惠顾' || record.prizeName === '谢谢参与') {\n                    return '谢谢参与'\n                }\n                return '未中奖'\n            }\n        },\n\n        // 获取空状态标题\n        getEmptyTitle() {\n            switch (this.currentTab) {\n                case 'winning':\n                    return '暂无中奖记录'\n                case 'unclaimed':\n                    return '暂无待领取奖品'\n                default:\n                    return '暂无抽奖记录'\n            }\n        },\n\n        // 获取空状态副标题\n        getEmptySubtitle() {\n            switch (this.currentTab) {\n                case 'winning':\n                    return '参与更多抽奖活动，赢取丰厚奖品'\n                case 'unclaimed':\n                    return '您的奖品都已领取完毕'\n                default:\n                    return '快去参与抽奖活动吧'\n            }\n        },\n\n        // 格式化日期时间\n        formatDateTime(dateTime) {\n            return formatDateTime(dateTime)\n        },\n\n        // 颜色调整工具方法\n        adjustColor(color, amount) {\n            // 将十六进制颜色转换为RGB\n            const hex = color.replace('#', '')\n            const r = parseInt(hex.substr(0, 2), 16)\n            const g = parseInt(hex.substr(2, 2), 16)\n            const b = parseInt(hex.substr(4, 2), 16)\n\n            // 调整亮度\n            const newR = Math.max(0, Math.min(255, r + amount))\n            const newG = Math.max(0, Math.min(255, g + amount))\n            const newB = Math.max(0, Math.min(255, b + amount))\n\n            // 转换回十六进制\n            return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`\n        }\n    }\n}\n</script>\n\n<style lang=\"scss\">\n/* 全局样式 - 确保页面背景完全覆盖 */\npage {\n    height: 100%;\n    background: transparent;\n}\n</style>\n\n<style lang=\"scss\" scoped>\n.records-page {\n    min-height: 100vh;\n    height: auto;\n    /* 背景色通过内联样式动态设置 */\n    padding-bottom: 20rpx;\n    position: relative;\n    width: 100%;\n    box-sizing: border-box;\n}\n\n\n\n/* 筛选标签 */\n.filter-tabs {\n    display: flex;\n    background: rgba(255, 255, 255, 0.95);\n    margin: 20rpx 30rpx 20rpx;\n    border-radius: 25rpx;\n    padding: 8rpx;\n    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);\n\n    .tab-item {\n        flex: 1;\n        text-align: center;\n        padding: 20rpx 10rpx;\n        border-radius: 20rpx;\n        position: relative;\n        transition: all 0.3s ease;\n\n        text {\n            font-size: 28rpx;\n            color: #666;\n            font-weight: 500;\n        }\n\n        .badge {\n            position: absolute;\n            top: 10rpx;\n            right: 15rpx;\n            background: #ff4757;\n            color: white;\n            font-size: 20rpx;\n            padding: 4rpx 8rpx;\n            border-radius: 10rpx;\n            min-width: 20rpx;\n            text-align: center;\n        }\n\n        &.active {\n            background: linear-gradient(135deg, #667eea, #764ba2);\n            box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.4);\n\n            text {\n                color: white;\n                font-weight: bold;\n            }\n\n            .badge {\n                background: #ff6b7a;\n            }\n        }\n    }\n}\n\n/* 加载状态 */\n.loading-container {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    justify-content: center;\n    padding: 100rpx 0;\n\n    .loading-spinner {\n        width: 60rpx;\n        height: 60rpx;\n        border: 4rpx solid rgba(255, 255, 255, 0.3);\n        border-top: 4rpx solid #ffffff;\n        border-radius: 50%;\n        animation: spin 1s linear infinite;\n        margin-bottom: 20rpx;\n    }\n\n    .loading-text {\n        color: rgba(255, 255, 255, 0.8);\n        font-size: 28rpx;\n    }\n}\n\n@keyframes spin {\n    0% {\n        transform: rotate(0deg);\n    }\n\n    100% {\n        transform: rotate(360deg);\n    }\n}\n\n/* 记录容器 */\n.records-container {\n    padding: 0 30rpx;\n}\n\n/* 空状态 */\n.empty-state {\n    text-align: center;\n    padding: 100rpx 40rpx;\n\n    .empty-image {\n        width: 200rpx;\n        height: 200rpx;\n        margin-bottom: 40rpx;\n        opacity: 0.6;\n    }\n\n    .empty-title {\n        display: block;\n        font-size: 32rpx;\n        color: rgba(255, 255, 255, 0.9);\n        font-weight: bold;\n        margin-bottom: 15rpx;\n    }\n\n    .empty-subtitle {\n        display: block;\n        font-size: 26rpx;\n        color: rgba(255, 255, 255, 0.7);\n        margin-bottom: 40rpx;\n    }\n\n    .empty-action {\n        .btn {\n            display: inline-block;\n            padding: 25rpx 50rpx;\n            border-radius: 25rpx;\n            font-size: 28rpx;\n            font-weight: bold;\n\n            &.btn-primary {\n                background: linear-gradient(135deg, #ff6b7a, #ff8e9b);\n                color: white;\n                box-shadow: 0 8rpx 25rpx rgba(255, 107, 122, 0.4);\n            }\n        }\n    }\n}\n\n/* 记录列表 */\n.records-list {\n    .record-item {\n        background: rgba(255, 255, 255, 0.95);\n        border-radius: 20rpx;\n        margin-bottom: 20rpx;\n        overflow: hidden;\n        box-shadow: 0 8rpx 25rpx rgba(0, 0, 0, 0.1);\n        transition: all 0.3s ease;\n\n        &:active {\n            transform: scale(0.98);\n            box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.15);\n        }\n\n        .record-card {\n            padding: 30rpx;\n            display: flex;\n            align-items: center;\n            justify-content: space-between;\n\n            .prize-info {\n                display: flex;\n                align-items: center;\n                flex: 1;\n\n                .prize-icon {\n                    width: 80rpx;\n                    height: 80rpx;\n                    margin-right: 20rpx;\n                    border-radius: 15rpx;\n                    overflow: hidden;\n                    background: #f5f5f5;\n                    display: flex;\n                    align-items: center;\n                    justify-content: center;\n\n                    .icon-img {\n                        width: 60rpx;\n                        height: 60rpx;\n                    }\n                }\n\n                .prize-details {\n                    flex: 1;\n\n                    .prize-name {\n                        display: block;\n                        font-size: 32rpx;\n                        font-weight: bold;\n                        color: #333;\n                        margin-bottom: 8rpx;\n                    }\n\n                    .prize-desc {\n                        display: block;\n                        font-size: 24rpx;\n                        color: #666;\n                        margin-bottom: 8rpx;\n                        line-height: 1.4;\n                    }\n\n                    .draw-time {\n                        display: block;\n                        font-size: 24rpx;\n                        color: #999;\n                    }\n                }\n            }\n\n            .status-section {\n                display: flex;\n                flex-direction: column;\n                align-items: flex-end;\n                gap: 15rpx;\n\n                .status-badge {\n                    padding: 8rpx 16rpx;\n                    border-radius: 20rpx;\n                    font-size: 24rpx;\n\n                    .status-text {\n                        font-weight: 500;\n                    }\n\n                    &.won {\n                        background: linear-gradient(135deg, #ff6b7a, #ff8e9b);\n                        color: white;\n                    }\n\n                    &.claimed {\n                        background: linear-gradient(135deg, #2ed573, #7bed9f);\n                        color: white;\n                    }\n\n                    &.unclaimed {\n                        background: linear-gradient(135deg, #ffa726, #ffcc02);\n                        color: white;\n                        animation: pulse 2s infinite;\n                    }\n\n                    &.thanks {\n                        background: linear-gradient(135deg, #f39c12, #e67e22);\n                        color: white;\n                    }\n\n                    &.not-won {\n                        background: #f1f2f6;\n                        color: #666;\n                    }\n                }\n\n\n            }\n        }\n\n        .activity-info {\n            padding: 0 30rpx 20rpx;\n            border-top: 1rpx solid #f0f0f0;\n            margin-top: 10rpx;\n            padding-top: 20rpx;\n\n            .activity-name {\n                font-size: 24rpx;\n                color: #666;\n                font-style: italic;\n            }\n        }\n    }\n}\n\n@keyframes pulse {\n    0% {\n        box-shadow: 0 0 0 0 rgba(255, 167, 38, 0.7);\n    }\n\n    70% {\n        box-shadow: 0 0 0 10rpx rgba(255, 167, 38, 0);\n    }\n\n    100% {\n        box-shadow: 0 0 0 0 rgba(255, 167, 38, 0);\n    }\n}\n\n/* 刷新提示 */\n.refresh-tip {\n    text-align: center;\n    padding: 20rpx;\n    color: rgba(255, 255, 255, 0.8);\n    font-size: 26rpx;\n}\n\n/* 底部提示 */\n.bottom-tip {\n    text-align: center;\n    padding: 40rpx 20rpx;\n    color: rgba(255, 255, 255, 0.6);\n    font-size: 24rpx;\n}\n\n/* 响应式适配 */\n@media screen and (max-width: 750rpx) {\n    .record-card {\n        .prize-info {\n            .prize-icon {\n                width: 70rpx;\n                height: 70rpx;\n                margin-right: 15rpx;\n\n                .icon-img {\n                    width: 50rpx;\n                    height: 50rpx;\n                }\n            }\n\n            .prize-details {\n                .prize-name {\n                    font-size: 30rpx;\n                }\n\n                .prize-desc {\n                    font-size: 22rpx;\n                }\n\n                .draw-time {\n                    font-size: 22rpx;\n                }\n            }\n        }\n\n        .status-section {\n            .status-badge {\n                font-size: 22rpx;\n                padding: 6rpx 12rpx;\n            }\n\n            .claim-btn {\n                font-size: 22rpx;\n                padding: 10rpx 20rpx;\n            }\n        }\n    }\n}\n</style>\n", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./records.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./records.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754186977514\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./records.vue?vue&type=style&index=1&id=00c304e8&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./records.vue?vue&type=style&index=1&id=00c304e8&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754186977513\n      var cssReload = require(\"/Applications/HBuilderX.app/Contents/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}